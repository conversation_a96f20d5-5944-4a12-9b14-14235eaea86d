import { Dispatch, /* lazy, */ SetStateAction, /*  Suspense, */ useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@mui/material";
import { useStyle as usePageStyle } from "@pages/crm/style";
import { useStyle } from "./style";
// import { AccountDocumentList } from "@features/crm/AccountDocumentList/AccountDocumentList";
// import { Profile } from "@features/crm/AccountDetails/Profile/Profile";
import { IPartialContact } from "@features/crm/CustomerDetails/Contact/IContact";
// import { ProfileSkeleton } from "@features/crm/AccountDetails/Profile/ProfileSkeleton";
import { CLIENTS_OF_KEYCLOAK, CRM_ACCOUNT_DETAILS_URL_LIST } from "@constants";
// import { Order } from "@features/crm/Order/Order";
import { AccountDetailsHeader } from "@features/crm/AccountDetails/AccountDetailsHeader/AccountDetailsHeader";
import { /* HandleErrorAndLoading, */ INavTab, NavigationTabs /* , SearchSkeleton */ } from "@common";
import { AccountPaymentAndBilling } from "@features/crm/AccountPaymentAndBilling/AccountPaymentAndBilling";
import { IAccountDetails } from "@features/crm/CustomerPanel/ICustomer";
// import { Transactions } from "@features/crm/AccountTransaction/Transactions";
import { IFetchedData, keycloakUtils } from "itsf-ui-common";
// import { Collection } from "@features/crm/Collection/Collection";
import { ICollection } from "@modules/collection/interfaces";
// import { AccountLevelAddons } from "@features/crm/Addons/Postpay/AccountLevelAddons";
import { INationalityEdit } from "@features/crm/CustomerDetails/Nationality/INationality";
import { IAddress } from "@common/Address/IAddress";
import { ICommunicationPreferences, TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { useAuth } from "@hooks/useAuth";
import { SubscriptionsAddOns } from "@features/crm/SubscriptionsAddOns/SubscriptionsAddOns";
// import InformationSchedule from "@features/crm/InformationSchedule/InformationSchedule";
import { IBillCycle } from "@features/crm/AccountDetails/AccountDetailsHeader/Billing/BillCycle/IBillCycle";
import { DynamicTicketList } from "@modules/caseManagement/DynamicTicketList";
import InformationSchedule from "@features/crm/InformationSchedule/InformationSchedule";
import {Order} from "@features/crm/Order/Order";
import ViewAdjusments from "@features/crm/Adjusments/ViewAdjusments";

// const CustomerHistory = lazy(() =>
//     import("../../../../features/crm/CustomerHistory/CustomerHistory").then((module) => ({
//         default: module.CustomerHistory,
//     }))
// );

interface IParams {
    accountId: string;
    authorizedUserIds: IFetchedData<string[] | undefined>;
    contactPayerUuid?: string;
    collection: IFetchedData<ICollection | undefined>;
    account: IFetchedData<IAccountDetails | undefined>;
    setAccount: Dispatch<SetStateAction<IAccountDetails>>;
    contactDetails?: TContactDetails;
    updateOwner(newOwner: IPartialContact): void;
    getContact(): void;
    updateBillingAddress(newAddress: IAddress): void;
    updateCommunicationPreferences(newCommunicationPreferences: ICommunicationPreferences): void;
    getCollectionStatus(): void;

    updateNationality(newNationality: INationalityEdit): void;

    billCycle: IBillCycle | undefined;
    // accountsCbs: ITigoCbsAccountsResponse | undefined;
    accountsCbs: undefined;
}

export const AccountDetailsPage = (props: IParams) => {
    const {
        // collection,
        account,
        setAccount,
        contactDetails,
        contactPayerUuid,
        // authorizedUserIds,
        // updateOwner,
        // updateNationality,
        accountId,
        // updateBillingAddress,
        // getCollectionStatus,
        // updateCommunicationPreferences,
        billCycle,
        accountsCbs,
    } = props;
    const { classes: pageClasses } = usePageStyle();
    const { classes } = useStyle();
    const { t } = useTranslation(["customer", "common"]);
    const [navTabList, setNavTabList] = useState<INavTab[]>([]);

    // useEffect(() => {
    //     setNavTabList([
    //         // {
    //         //     component: (
    //         //         <HandleErrorAndLoading
    //         //             error={authorizedUserIds.state.error}
    //         //             isLoading={authorizedUserIds.state.isLoading}
    //         //             skeleton={<ProfileSkeleton />}
    //         //         >
    //         //             {contactDetails && (
    //         //                 <Profile
    //         //                     accountId={accountId}
    //         //                     authorizedUserIds={authorizedUserIds}
    //         //                     contactDetails={contactDetails}
    //         //                     updateBillingAddress={updateBillingAddress}
    //         //                     updateCommunicationPreferences={updateCommunicationPreferences}
    //         //                     updateNationality={updateNationality}
    //         //                     updateOwner={updateOwner}
    //         //                 />
    //         //             )}
    //         //         </HandleErrorAndLoading>
    //         //     ),
    //         //     label: t("customer:profile"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.PROFILE_PAGE_URL,
    //         // },
    //         {
    //             component: (
    //                 <AccountPaymentAndBilling
    //                     account={account}
    //                     accountId={accountId}
    //                     contact={contactDetails}
    //                     contactOwnerUuid={contactDetails?.contactUuid}
    //                     contactPayerUuid={contactPayerUuid}
    //                     setAccount={setAccount}
    //                 />
    //             ),
    //             label: t("customer:paymentAndBilling"),
    //             path: CRM_ACCOUNT_DETAILS_URL_LIST.PAYMENT_AND_BILLING_PAGE_URL,
    //         },
    //         {
    //             component: <InformationSchedule accountId={accountId} contactDetails={contactDetails} />,
    //             label: t("customer:InformationSchedule"),
    //             path: CRM_ACCOUNT_DETAILS_URL_LIST.INFORMATION_SCHEDULE,
    //         },
    //         // {
    //         //     component: <Transactions accountId={accountId} getAccountDetailsState={account.state} />,
    //         //     label: t("customer:transactions"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.TRANSACTIONS_PAGE_URL,
    //         // },
    //         // {
    //         //     component: <AccountLevelAddons accountId={accountId} />,
    //         //     label: t("customer:manageAddons"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.ACCOUNT_ADDONS_PAGE_URL,
    //         // },
    //         // {
    //         //     component: (
    //         //         <Collection
    //         //             accountId={accountId}
    //         //             collection={collection}
    //         //             getCollectionStatus={getCollectionStatus}
    //         //             isAccountDetailsLoading={account.state.isLoading}
    //         //         />
    //         //     ),
    //         //     label: t("customer:collection"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.COLLECTION_PAGE_URL,
    //         // },
    //         // {
    //         //     component: <Order accountId={accountId} />,
    //         //     label: t("customer:order"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.ORDER_PAGE_URL,
    //         // },
    //         // {
    //         //     component: <AccountDocumentList accountId={accountId} getAccountDetailsState={account.state} />,
    //         //     label: t("customer:documents"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.DOCUMENT_PAGE_URL,
    //         // },
    //         // {
    //         //     component: (
    //         //         <Suspense fallback={<SearchSkeleton height={200} />}>
    //         //             <HandleErrorAndLoading
    //         //                 error={authorizedUserIds.state.error}
    //         //                 isLoading={authorizedUserIds.state.isLoading}
    //         //                 skeleton={<SearchSkeleton height={200} />}
    //         //             >
    //         //                 <CustomerHistory accountId={accountId} customerUuid={contactDetails?.contactUuid} />
    //         //             </HandleErrorAndLoading>
    //         //         </Suspense>
    //         //     ),
    //         //     label: t("common:customerHistory"),
    //         //     path: CRM_ACCOUNT_DETAILS_URL_LIST.CUSTOMER_HISTORY_PAGE_URL,
    //         // },
    //     ]);
    // }, []);

    const { userHasRoles } = useAuth({
        permissionType: "customer",
        roles: ["VIEW_SUBSCRIPTIONS"],
    });

    useEffect(() => {
        setNavTabList(() => {
            const tabs: INavTab[] = [
                {
                    component: contactDetails ? (
                        <AccountPaymentAndBilling
                            account={account}
                            accountId={accountId}
                            contact={contactDetails}
                            contactOwnerUuid={contactDetails.contactUuid}
                            contactPayerUuid={contactPayerUuid}
                            setAccount={setAccount}
                        />
                    ) : (
                        <Typography>{t("common:loading")}</Typography>
                    ),
                    label: t("customer:paymentAndBilling"),
                    path: CRM_ACCOUNT_DETAILS_URL_LIST.PAYMENT_AND_BILLING_PAGE_URL,
                },
                 {
                     component: contactDetails ? (
                         <InformationSchedule account={account} accountId={accountId} contactDetails={contactDetails} />
                     ) : (
                         <Typography>{t("common:loading")}</Typography>
                     ),
                     label: t("customer:InformationSchedule"),
                    path: CRM_ACCOUNT_DETAILS_URL_LIST.INFORMATION_SCHEDULE,
                 },
                 {
                     component: contactDetails ? (
                         <ViewAdjusments accountId={accountId} />
                     ) : (
                         <Typography>{t("common:loading")}</Typography>
                     ),
                     label: t("customer:Adjusments"),
                    path: CRM_ACCOUNT_DETAILS_URL_LIST.ADJUSMENTS_PAGE_URL,
                 },


            ];

            let insertIndex = 2;
            if (userHasRoles && contactDetails && accountId && billCycle) {
                tabs.splice(insertIndex, 0, {
                    component: (
                        <SubscriptionsAddOns
                            account={account}
                            accountId={accountId}
                            accountsCbs={accountsCbs}
                            billCycle={billCycle}
                            contactDetails={contactDetails}
                        />
                    ),
                    label: t("customer:subscriptionsAddOns"),
                    path: CRM_ACCOUNT_DETAILS_URL_LIST.SUBSCRIPTIONS_ADD_ONS,
                });
                insertIndex++;
                tabs.splice(insertIndex, 0, {
                    component: <Order accountId={accountId} />,
                    label: t("customer:orders"),
                    path: CRM_ACCOUNT_DETAILS_URL_LIST.ORDER_PAGE_URL,
                });
                insertIndex++;
            }

            if (keycloakUtils.getUserResourceAccess()?.[CLIENTS_OF_KEYCLOAK.CASE_MANAGEMENT] !== undefined) {
                tabs.push({
                    component: <DynamicTicketList accountId={accountId} />,
                    label: t("customer:caseManagement"),
                    path: CRM_ACCOUNT_DETAILS_URL_LIST.CASE_MANAGEMENT_PAGE_URL,
                });
            }

            return tabs;
        });
    }, [userHasRoles, t, contactDetails, accountId, accountsCbs, billCycle, account, contactPayerUuid, setAccount]);

    return (
        <div className={pageClasses.root}>
            <div className={pageClasses.heading}>
                <div className={classes.title}>
                    <Typography variant="h1">{t("customer:accountDetails")}</Typography>
                    {account.value?.legacyReference && (
                        <div className={classes.legacyIdChip}>
                            <Typography variant="caption">
                                {t("customer:legacyAccountId")}:&nbsp;
                                <span className={classes.legacyId}>{account.value?.legacyReference}</span>
                            </Typography>
                        </div>
                    )}
                </div>
                <AccountDetailsHeader
                    accountId={accountId}
                    contactUuid={contactDetails?.contactUuid}
                    getAccountDetailsState={account.state}
                />
            </div>
            <div className={pageClasses.content}>
                {navTabList.length > 0 && (
                    <NavigationTabs
                        navTabList={navTabList || []}
                        redirectUrl={CRM_ACCOUNT_DETAILS_URL_LIST.PROFILE_PAGE_URL}
                    />
                )}
            </div>
        </div>
    );
};
